<template>
    <div class="container" v-loading="writeState.loading">
      <div class="left">
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.quesId" class="test-content-ques">
                <div class="squre"></div>
                <div class="test-tittle" style="display: flex;">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
                
                <!-- 选择题答题区域 -->
                <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="paper-content-ques">
                    <div v-if="item.ques.cate == 1">
                        <el-checkbox-group v-model="item.ques.userJson" class="checkbox-style-checkbox">
                            <el-checkbox
                                v-for="(it,ind) in item.ques.options"
                                :key="ind"
                                @change="checkChange(ind,index)"
                                :label="ind"
                                size="large" border
                            >
                            {{ String.fromCharCode(65 + ind) }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <div v-else>
                        <el-checkbox-group v-model="item.ques.userJson" class="checkbox-style-checkbox">
                            <el-checkbox
                                v-for="(it,ind) in item.ques.options"
                                :key="ind"
                                :label="ind"
                                @change="checkChangeM(item)"
                                size="large" border
                            >
                            {{ String.fromCharCode(65 + ind) }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </div>
                
                <!-- 主观题答题区域 -->
                <div v-else class="paper-content-ques">
                    <div v-if="writeState.showSubjective">
                        <div class="show-analyse">
                            <el-switch size="small" @change="togAnswer(item,item.showAnalyse)" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                        </div>
                        <div v-show="item.showAnalyse" class="analyse">
                            <div class="flex-sty">
                                <span>【知识点】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.pointVos[0]?.name || '--'" />
                            </div>
                            <div class="flex-sty">
                                <span>【答案】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.displayAnswer" />
                            </div>
                            <div class="flex-sty">
                                <span>【分析】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.analyse" />
                            </div>
                            <div class="flex-sty">
                                <span>【解答】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.method" />
                            </div>
                            <div class="flex-sty">
                                <span>【点评】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.discuss" />
                            </div>
                        </div>
                        
                        <div class="answer-img-box">
                            <el-image
                                class="answer-img"
                                v-for="(it, ind) in item.userAnswer"
                                style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                :src="it"
                                :zoom-rate="1.2"
                                :max-scale="7"
                                :min-scale="0.2"
                                :preview-src-list="item.userAnswer"
                                show-progress
                                :initial-index="ind"
                                fit="cover"
                            />
                        </div>
                        
                        <div class="answers">
                            <div class="answer-box" @click="correcthandle(index, 1)" :class="item.userMark == 1?'green-box':''">
                                <div></div>正确
                            </div>
                            <div class="answer-box" @click="correcthandle(index, 2)" :class="item.userMark == 2?'yellow-box':''">
                                <div></div>半对
                            </div>
                            <div class="answer-box" @click="correcthandle(index, 0)" :class="item.userMark == 0?'red-box':''">
                                <div></div>错误
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="show-analyse">
                            <el-switch size="small" @change="togAnswer(item,item.showAnalyse)" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                        </div>
                        <div v-show="item.showAnalyse" class="analyse">
                            <div class="flex-sty">
                                <span>【知识点】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.pointVos[0]?.name || '--'" />
                            </div>
                            <div class="flex-sty">
                                <span>【答案】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.displayAnswer" />
                            </div>
                            <div class="flex-sty">
                                <span>【分析】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.analyse" />
                            </div>
                            <div class="flex-sty">
                                <span>【解答】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.method" />
                            </div>
                            <div class="flex-sty">
                                <span>【点评】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.discuss" />
                            </div>
                        </div>
                        <uploadAnswerImg :imgList="item.ques.userJson" :index="index" @getImgList="handleImgList" />
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div>
            <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" 
                 :class="getNumberItemClass(item)"
                 @click="scrollToQuestion(index)"> {{ index + 1 }} </div>
            <div v-show="!writeState.showSubjective || writeState.isAllCorrect" class="icon-btn size285" :class="writeState.disabled?'disabled':''" @click="handleSubmit" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
            </div>
        </div>
      </div>
      <div class="five-step-box" v-if="writeState.showStep">
        <fiveStep :sourceId="queryData.sourceId" :type="1" :update="false" @sendStep="sendStep"></fiveStep>
      </div>
    </div>
    
    <!-- 提交确认弹窗 -->
    <el-dialog class="dialog-correct" v-model="writeState.showDialog" title="提交" align-center center>
        <p class="black-text">{{ writeState.subjectiveNum }}道主观题需要对照答案批改</p>
        <p class="grey-text">（同学们可以邀请家长一起完成哦）</p>
        <template #footer>
        <div class="dialog-footer">
            <div class="blue-btn" @click="toCorrect">去批改</div>
        </div>
        </template>
    </el-dialog>
</template>
  
<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/index.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi} from "@/api/video"
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { createTrainToAtlasApi, getDetailsApi, saveToAtlasApi, checkTrainQuesApi, getDetailssApi, saveUserJsonApi,saveToIntroduceddApi } from '@/api/training'
const route = useRoute()
const router = useRouter()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    showCorrect: true,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0, // 上次计时时间戳
    showDialog: false, // 提交确认弹窗
    subjectiveNum: 0, // 需要批改的主观题数量
    unWriteNum: [] as number[], // 未答题目编号
    showSubjective: false, // 是否显示主观题批改模式
    isAllCorrect: true // 是否所有主观题都已批改
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref([] as AData[])
watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }

})


// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}
onMounted(() => {
    // 初始化时间戳
    writeState.lastTimestamp = Date.now()
    
    getDetails()
    
    // 启动计时器
    timer = setInterval(() => {
        timeState.seconds++
        writeState.itemTimer++ // 更新单题计时
    }, 1000)
    
    window.customGoBack = customGoBack
})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0
    
    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 隐藏积分
const jfHide = () => {
    writeState.jfShow = false
}
// 获取学习步骤
const sendStep = ( data: number) => {
    writeState.step = data
}

// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

  //显示答案
const togAnswer = async (item:any,isShow:any) => { 
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }   
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true
            
            const response = await quesGetApi({id: item.ques.quesId}) as any
            
            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)
                
                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}

const createTrain = () => {
    const formdata = new FormData()
    formdata.append("sourceId", queryData.sourceId)
    formdata.append("noteSource", '1')
    // for(let i of queryData.reportId){
    //   formdata.append("pointIds[]", i)
    // }
    formdata.append("step", queryData.step)
    createTrainToAtlasApi(formdata).then((res: any) => {
        if (res.data) {
            writeState.trainingId = res.data
            writeState.showStep = true
            
            // 获取详情
            getDetails()
            
        }

    }).catch((error) => {
    })
}
const getDetails = () => {
    writeState.loading = true
    writeState.showCorrect = true
    getDetailssApi({trainingId: queryData.reportId}).then((res1: any) => {
        if (res1.code == 200) {
            detailData = res1.data
            timeState.seconds = Number(res1.data.trainTime) / 1000
            res1.data.items.forEach((item) => {
                item.showAnalyse = false
                item.ques.userJson = []
                item.userAnswer = item.userAnswer || []
            })
            allTest.value = res1.data.items
            
            // 检查是否还有未完成的题目
            const hasUnansweredQuestions = allTest.value.some(item => 
                !item.ques.userJson || item.ques.userJson.length === 0
            )
            writeState.disabled = hasUnansweredQuestions
        }
        writeState.loading = false
    }).catch((error) => {
        writeState.loading = false
    })
}

// 获取题号样式类
const getNumberItemClass = (item: any) => {
    if (item.ques.userJson && item.ques.userJson.length > 0) {
        return 'blue' // 已答题
    }
    return '' // 未答题
}

// 滚动到指定题目
const scrollToQuestion = (index: number) => {
    const element = document.querySelectorAll('.test-content-ques')[index]
    if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
}

// 单选题选择变化
const checkChange = (val: any, index: number) => {
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`)
        return
    }

    const currentItem = allTest.value[index]?.ques
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`)
        return
    }

    currentItem.userJson = [val]
    checkAnswerStatus()
}

// 多选题选择变化
const checkChangeM = (item: any) => {
    checkAnswerStatus()
}

// 检查答题状态
const checkAnswerStatus = () => {
    const hasUnansweredQuestions = allTest.value.some(item => 
        !item.ques.userJson || item.ques.userJson.length === 0
    )
    writeState.disabled = hasUnansweredQuestions
}

// 批改选择题
const checkSelect = () => {
    return new Promise((resolve, reject) => {
        try {
            allTest.value.forEach((item: any, index: number) => {
                // 只处理单选题(cate=1)和多选题(cate=3)
                if (item.ques.cate == 1 || item.ques.cate == 3) {
                    // 用户未作答的情况
                    if (!item.ques.userJson || item.ques.userJson.length == 0) {
                        item.userMark = 0 // 未作答记为错误
                    }
                    // 单选题批改逻辑
                    else if (item.ques.cate == 1) {
                        const userAnswers = item.ques.userJson.toString()
                        const correctAnswers = item.ques.answers.toString()
                        item.userMark = (userAnswers == correctAnswers ? 1 : 0)
                    }
                    // 多选题批改逻辑
                    else if (item.ques.cate == 3) {
                        const userAnswers = item.ques.userJson.sort()
                        const correctAnswers = item.ques.answers?.sort()

                        // 完全正确
                        if (userAnswers.join(',') === correctAnswers.join(',')) {
                            item.userMark = 1
                        }
                        // 部分正确(用户答案是正确答案的子集)
                        else if (userAnswers.every(ans => correctAnswers.includes(ans))) {
                            item.userMark = 2 // 半对
                        }
                        // 错误
                        else {
                            item.userMark = 0
                        }
                    }
                }
            })
            resolve(true)
        } catch (error) {
            reject(error)
        }
    })
}

// 提交处理
const handleSubmit = () => {
    if (writeState.showSubjective) {
        submit()
    } else {
        writeState.unWriteNum = []
        writeState.subjectiveNum = 0
        
        allTest.value.map((item: any, index: number) => {
            if (!item.ques.userJson || item.ques.userJson.length == 0) {
                writeState.unWriteNum.push(index + 1)
            }
            if (item.ques.cate != 1 && item.ques.cate != 3) {
                writeState.subjectiveNum++
            }
        })
        
        if (writeState.unWriteNum.length > 0) {
            ElMessageBox.confirm(`第${writeState.unWriteNum.toString().replaceAll(',', '、 ')}题未作答，您确定要提交吗？`, '提示', {
                confirmButtonText: '继续答题',
                cancelButtonText: '确定提交',
                distinguishCancelAndClose: true,
                center: true
            }).then(() => {
                // 继续答题
            }).catch((action: Action) => {
                if (action == 'cancel') {
                    if (writeState.subjectiveNum > 0) {
                        writeState.showDialog = true
                    } else {
                        submit()
                    }
                }
            })
        } else {
            if (writeState.subjectiveNum > 0) {
                writeState.showDialog = true
            } else {
                submit()
            }
        }
    }
}

// 去批改主观题
const toCorrect = () => {
    writeState.showDialog = false
    writeState.showSubjective = true
    writeState.isAllCorrect = false
    
    // 转换主观题答案格式
    allTest.value.forEach((item: any) => {
        if (item.ques.cate != 1 && item.ques.cate != 3) {
            const urlArr = item.ques.userJson || []
            item.userAnswer = urlArr.map((img: any) => img.url || img)
        }
    })
}

// 主观题批改
const correcthandle = (index: number, userMark: number) => {
    if (index < 0 || index >= allTest.value.length) {
        return
    }

    const currentItem = allTest.value[index]
    if (!currentItem) {
        return
    }

    currentItem.userMark = userMark
    
    // 检查是否所有主观题都已批改
    writeState.isAllCorrect = true
    allTest.value.forEach((item: any) => {
        if (item.ques.cate != 1 && item.ques.cate != 3) {
            if (item.userMark == null) {
                writeState.isAllCorrect = false
            }
        }
    })
}

//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" + sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.ques.content)
  return tittle
}

//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}

//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}

// 最终提交
const submit = async () => {
    writeState.btnloading = true
    
    try {
        // 批改选择题
        await checkSelect()
        
        const items = allTest.value.map((item: any, index: number) => {
            let userJson = item.ques.userJson || []
            if (item.ques.cate != 1 && item.ques.cate != 3) {
                userJson = userJson.map((img: any) => img.url || img)
            }
            
            return {
                cate: item.ques.cate,
                trainingItemId: item.trainingItemId,
                userJson: userJson,
                userMark: item.userMark,
            }
        })

        const finalTrainTime = timeState.seconds * 1000
        
        const params = {
            trainingId: detailData.trainingId,
            trainTime: finalTrainTime,
            items,
            reviseCount: detailData.reviseCount || null,
            status: detailData.status,
            score: detailData.score,
            correctRate: detailData.correctRate,
        }

        const res = await saveToIntroduceddApi(params)
        
        if (timer !== null) {
            clearInterval(timer)
            timer = null
        }
        writeState.itemTimer = 0
        
        writeState.btnloading = false
        router.push({
            path: '/ai_percision/entrance_assessment/test_report',
            query: {
                data: dataEncrypt({
                    reportId: detailData.trainingId,
                    pageSource: '1'
                }),
            }
        })
    } catch (error) {
        writeState.btnloading = false
        ElMessage({
            message: '提交失败，请重试',
            type: 'error'
        })
    }
}
// 获取训练报告数据
// const getTringDetail = () => {
//     // 生成诊断报告弹窗暂未加上

//     getDetailsApi({trainingId: writeState.trainingId}).then((res: any) => {
//         if (res.code == 200) {
//             const data = res.data
//             localStorage.setItem('diagnosticReport', JSON.stringify(data))
//             writeState.btnloading = false
//             writeState.jfNum = res.data.integral
//             writeState.jfShow = true
//             writeState.jfHide = false
//             setTimeout(()=>{
//                 router.push({
//                     path: '/ai_percision/knowledge_graph_detail/training_report',
//                     query: { 
//                         data: dataEncrypt({
//                             sourceId: queryData.sourceId,
//                             showStep: '1'
//                         })
//                     }
//                 })
//             },3000)
//         }
//     }).catch((error) => {
//     })
// }
const handleAnalysis = () => {
    router.push({ 
        path: '/ai_percision/knowledge_graph_detail/paper_analysis'
    })
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        width: 60.3125rem;
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
            box-sizing: border-box;
            overflow-y: auto;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0;
                margin-bottom: .625rem;
                position: relative;
                color: #999999;
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                box-sizing: border-box;
            }
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .red {
                background: #dd2a2a;
                color: #ffffff;
            }
            .green {
                background: #00c9a3;
                color: #ffffff;
            }
            .yellow {
                background: #f1be21;
                color: #ffffff;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}

.dialog-correct {
    width: 34.875rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
}

.black-text {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 400;
    text-align: center;
}

.grey-text {
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
}

.dialog-footer {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    cursor: pointer;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    text-align: center;
    font-size: 1rem;
    font-weight: 400;
}
</style>