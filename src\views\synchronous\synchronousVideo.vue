<!-- 名师课堂-知识点视频-章节进入 -->
<template>
  <div class="content" id="content">

    <div class="inner">
      <div class="wrap">
        <div class="menu_lt" :class="{ 'full-width': isFromCollection }">
          <div class="wk_box">
            <div class="wk_tit">
              <div class="wk_p" v-html="ReplaceMathString(state?.vidInfo?.title || state?.vidInfo?.videoName || state?.vidInfo?.videoName3)"></div>
              <template v-if="state.vidType==1&&query?.sourceType !== 'collect'">
                <div class="wk_status status2" v-if="state?.vidInfo?.status==2">已学完</div>
                <div class="wk_status status1" v-else-if="state?.vidInfo?.status==1">未学完</div>
                <div class="wk_status status0" v-else>未学习</div>
              </template>
            </div>
            <!-- 播放器 -->
            <div id="dplayer1" class="wk_video" v-show="state.videoUrl|| state.videoList.length"></div>
            <div class="wk_nodata" v-show="!(state.videoUrl|| state.videoList.length)&&state.isShow">
              <img src="@/assets/img/teachroom/nopoint.png" />暂无名师同步学视频
            </div>
            <div class="wk_opt" v-show="state.videoUrl && !isFromCollection">
              <div class="wk_collect" :class="state?.vidInfo?.userCollect?'active':''" @click="setCollect">
                <img src="@/assets/img/teachroom/collect.svg" />
                <img src="@/assets/img/teachroom/collectsel.svg" />
              </div>
              <div class="wk_thumbs" :class="state?.vidInfo?.userLike?'active':''" @click="setThumbs">
                <img src="@/assets/img/teachroom/thumbs.svg" />
                <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                {{state?.vidInfo?.likeNum}}
              </div>
            </div>
          </div>
          <template v-if="state.vidInfo?.summary">
            <!-- 优学派h5 -->
            <iframe :src="state.vidInfo.summary" class="iframe" v-if="state.vidInfo.isHttp"></iframe>
            <!-- 学大html -->
            <div class="wkinfo" v-html="ReplaceMathString(state.vidInfo.summary)" v-else></div>
          </template>
        </div>
        <div class="menu_rt " :class="{ 'none': isFromCollection }">
          <div class="now_box">
            <div class="now_air">
              <img src="@/assets/img/teachroom/playing2.gif" />正在播放
            </div>
            <div class="now_img">
              <img :src="state.vidInfo.videoCover" style="transform: scale(1.1);" v-if="state.vidInfo?.videoCover" />
              <img src="@/assets/img/teachroom/novid.png" v-else />
              <template v-if="state.vidType==1">
                <div class="now_status status2" v-if="state.vidInfo.status==2">已学完</div>
                <div class="now_status status1" v-else-if="state.vidInfo.status==1">未学完</div>
                <div class="now_status status0" v-else>未学习</div>
              </template>
            </div>
            <div class="now_name" v-html="ReplaceMathString(state.vidInfo.videoName3||state.vidInfo.videoName)"></div>
          </div>
          <div class="vid_h2 nowrap" v-if="state.data.length">{{state.data[0].name}}</div>
          <div class="vid_ul">
            <template v-for="(item,index) in state.data" :key="index">
              <!-- <template v-for="(item2,index2) in item.videoList" :key="index2"> -->
                <!-- 带概述 -->
                <!-- <div class="vid_li " v-for="(item3,index3) in item.videoList" @click="wekePlay" :data-i="index"
                  :data-i2="index2" :data-i3="index3">
                  <div class="vid_img">
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item3.videoName)"></div>
                    <div class="vid_state">
                      <template v-if="item2.type==1">
                        <div class="vid_status status2" v-if="item3.studyStatus==2">已学完</div>
                        <div class="vid_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                        <div class="vid_status status0" v-else>未学习</div>
                      </template>
                      <div class="vid_h1"></div>
                      <div class="vid_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                    </div>
                  </div>
                </div> -->
                <!-- 无视频带概述 -->
                <!-- <div class="vid_li" v-if="!item2.videoList" @click="gaisuShow" :data-i="index" :data-i2="index2">
                  <div class="vid_img">
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item2.name)"></div>
                    <div class="vid_state">
                      <div class="vid_status status0"></div>
                      <div class="vid_h1"></div>
                    </div>
                  </div>
                </div> -->

              <!-- </template> -->
                   <!-- 例题 -->
                   <div class="vid_li" v-for="(item3,index3) in item.videoList" :key="index3" @click="wekePlay2(item3)">
                  <div class="vid_img chinese-sync-container" v-if="isChineseSyncStyle||isEnglishSyncStyle">
                    <div class="chinese-sync-card" >
                      <div class="english-sync-title" v-if="isEnglishSyncStyle" :style="{ backgroundImage: getSyncBackgroundImage }" v-html="ReplaceMathString(item3.title)"></div>

                      <div class="chinese-sync-title" v-else :style="{ backgroundImage: getSyncBackgroundImage }" v-html="ReplaceMathString(item3.title)"></div>
                    </div>

                  </div>
                  <div class="vid_img" v-else>
                    <img :src="item3.videoCover" class="wk_img" v-if="item3.videoCover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item3.title)"></div>
                    <div class="vid_state">
                      <div class="vid_status status2" v-if="item3.status==2">已学完</div>
                      <div class="vid_status status1" v-else-if="item3.status==1">未学完</div>
                      <div class="vid_status status0" v-else>未学习</div>
                      <div class="vid_h1" v-html="ReplaceMathString(item3.modal)"></div>
                      <div class="vid_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                    </div>
                  </div>
                </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="state.jfHide" :num="state.jfNum" :source="state.jfSource" @close="jfHide">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch,computed,onUnmounted } from 'vue';
  import router from '@/router/index'
  import { ElMessage } from "element-plus"
  import { useUserStore } from "@/store/modules/user"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString } from '@/utils/user/util'
  import { setStudyTimeApi, analyseVideoNumApi } from "@/api/user"
  import { getVideoListYxpApi, getVideoUrlApi, videoModifyApi, getVideoReviewNumApi, setUserVideoViewNumberApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import coinAlert from "@/views/components/coinAlert/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  import ywfont from '@/assets/img/synchronous/ywfont.png'
  import yyfont from '@/assets/img/synchronous/yyfont.png'
import { time } from 'console';

  defineOptions({
    name: "SynchronousVideo"
  })

  const route = useRoute()
  const query = reactive<any>(route.query)
  const state : any = reactive({
    showVip: false,
    dp: null,
    chapterId: '',
    subActive: '',
    vidInfo: {},
    isFirst: 1,
    isShow: false,
    data: [],
    poster: '',
    videoId: '',
    videoUrl: '',
    videoList: [], //视频
    videoIndex: 0,
    autoplay: false,
    subject: '',
    dataType: 0, //0带概述 1例题
    dataI: 0,
    dataI2: 0,
    dataI3: 0,
    isOne: '', //是否第一个视频
    vidType: '',
    //视频组件
    video: '',
    controls: false,
    videoBtn: true,
    playstate: 0, //0暂停，1播放
    speed: '1.0', //速度
    speedArr: ['0.5', '0.75', '1.0', '1.25', '1.5', '2.0'],
    rate: 0, //显示倍速
    playbtn: true,
    title: '',
    isVidMenu: 0,
    //进度条
    showSlider: true,
    showComp: 0,
    updateState: false,
    slider: 0,
    curtime: 0,
    nowtime: '00:00', // 当前时间
    endtime: '00:00', // 总时长
    duration: '', // 视频长度秒
    isFull: false,
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    source:null,
  })

onMounted(() => {
    init(route.query)
     window.customGoBack = customGoBack
  })

    // 自定义返回方法
  const customGoBack = () => {
      // 在返回时添加标记并传递当前章节ID
      if (state.chapterId) {
          // 添加查询参数
          router.replace({
              path: router.currentRoute.value.path,
              query: {
                  ...router.currentRoute.value.query,
                  fromVideo: 'true',
                  selectedChapterId: state.chapterId
              }
          }).then(() => {
              // 返回上一页
              setTimeout(() => {
                  router.go(-1);
              }, 100);
          });
      } else {
          // 没有章节ID时直接返回
          router.go(-1);
      }
  }
  onUnmounted(() => {
    // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
  })

// 计算属性：判断是否为语文同步学习样式
  const isChineseSyncStyle = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const moduleType = route.query.moduleType

    // 判断是否为语文科目
    const isChineseSubject = subjectType?.includes('chinese')

    // 判断是否为同步学习模块
    const isSyncModule = moduleType === '2'

    const isChineseSync = isChineseSubject && isSyncModule

    return isChineseSync
  })

  // 计算属性：判断是否来自收藏页面
  const isFromCollection = computed(() => {
    // 通过路由查询参数判断是否来自收藏
    return route.query.sourceType === 'collect'
  })

    // 计算属性：判断是否为语文同步学习样式
    const isEnglishSyncStyle = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const moduleType = route.query.moduleType

    // 判断是否为语文科目
    const isEnglishSubject = subjectType?.includes('english')

    // 判断是否为同步学习模块
    const isSyncModule = moduleType === '3'

    const isEnglishSync = isEnglishSubject && isSyncModule

    return isEnglishSync
  })

  // 计算属性：获取背景图片
  const getSyncBackgroundImage = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const isChineseSubject = subjectType?.includes('chinese')
    const isEnglishSubject = subjectType?.includes('english')

    if (isChineseSubject) {
      return `url(${ywfont})`
    } else if (isEnglishSubject) {
          return `url(${yyfont})`
     }
   })

  // 知识点讲解监听
  watch(() => state?.vidInfo?.summary, (newVal, oldVal) => {
    if (newVal) {
      setJfShow()
    }
  }, { immediate: true })

  // 显示积分-看知识点讲解
  const setJfShow = () => {
    state.jfShow = true
    state.jfHide = false
    state.jfNum = '3'
    state.jfSource = '1'
  }

  // 显示积分-看视频
  const setJfShow2 = () => {
    state.jfShow = true
    state.jfHide = true
    state.jfNum = '5'
    state.jfSource = '2'
  }

  // 隐藏积分
  const jfHide = () => {
    state.jfShow = false
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const quitHide = () => {
    state.showVip = false
    if (useUserStore().memberInfo) {
      init(route.query)
    }
  }

  const init = (query : any) => {
    if (query.id) {
      let { id, type, vid, subKey ,subject,source} = query
      setData({
        chapterId: id,
        subActive: subKey,
        videoId: vid,
        vidType: type,
        subject: subject,
        source
      })
      
      // 如果从收藏页面进入，直接使用视频信息，不获取视频列表
      if (query.sourceType === 'collect') {
        // 设置视频信息
        setData({
          vidInfo: {
            videoId: vid,
            title: query.videoTitle,
            type: type,
            source: source,
            status: 0, // 默认未学习状态
            videoCover: '', // 可能没有封面，使用默认
          }
        })
        // 直接获取视频URL
        getVideoUrl()
      } else {
        // 正常获取视频列表
        getVideoList()
      }
      
      //记录学习学科
      setLearnKey(state.subject)
    }
  }

  //获取知识点视频列表
  const getVideoList = (string?:string) => {
    const isBookId  =query.bookId
    const moduleType= query.moduleType || 1
    const { chapterId, subActive,videoId } = state
    const subject =query.subject
    const param = {
      subject,
      chapterId,
      chapterName:state.chapterName,
      type: 1,
      moduleType: moduleType,
      sourceId:chapterId,
      bookId:isBookId,
      videoIds: []
    }
    getVideoListYxpApi(param)
      .then((res : any) => {
        const res2 = res.data
        let vidInfo = {}
        if (res2?.xypVideos?.length>0) {

          res2.xypVideos=res2?.xypVideos?.map(item => {
            return {...item,name:res2.chapterName,chapterId:res2.chapterId}
          })
          if (videoId && chapterId) {
            const curChapterData = res2.xypVideos.find(item => item.chapterId = chapterId)
            if (curChapterData?.videoList?.length) {
              vidInfo=curChapterData.videoList?.find(item=>item.videoId==videoId)
            }
          }

          setData({
            data: res2.xypVideos,
            subject,
            vidInfo
          })
          if(!string)getVideoUrl()
        } else {
          setData({
            title: '',
            data: [],
            videoId: '',
            isShow: 1
          })
        }
      })
  }

  //判断视频播放次数
  const getVideoUrl = (data?:any) => {
    if (useUserStore().memberInfo) {
      getVidSrc(data?.videoId,data?.type,data?.source)
    } else {
      getVideoReviewNumApi().then((res : any) => {
        const num = res.data
        if (num < 2) {
          ElMessage.success(`剩余免费观看次数：${2 - num - 1}`)
          getVidSrc(data?.videoId,data?.type,data?.source)
        } else {
          setData({
            isShow: true
          })
          //开通会员弹窗
          state.showVip = true
        }
      })
    }
  }
  //获取知识点视频url
  const getVidSrc = (id?:string,type?:string,source?:string) => {
    const param = {
      type:type|| state.vidType, //1优学派 2菁优网
      videoId: id||state.videoId,
      source:source||state.source
    }
    getVideoUrlApi(param)
      .then((res : any) => {
        let data = res.data || ''
        setData({
          isShow: true,
          videoUrl: data,
          videoList: data ? [data] : []
        })
        if (state.isFirst) {
          //初始化视频控件
          initPlayers()
        } else {
          switchVideo()
        }
        setData({
          isFirst: 0
        })
        if (data) {
          videoPause()
        }
        setVideo()
        setStudyState(1)
        videoScoreSave()

      })
      .catch(() => {
        setData({
          isShow: true
        })
      })
  }
  //设置视频信息,默认第1个
  const setVideo = () => {
    const videos = state.videoList
    if (videos.length) {
      setData({
        videoUrl: videos[0],
        autoplay: true
      })
      videoPlay()
    } else {
      setData({
        videoUrl: '',
        autoplay: false
      })
    }
  }

  //记录视频播放次数
  const setUserVideoViewNumber = () => {
    const { chapterId, pointId, videoId, subject } = state
    const param = {
      time: 0,
      videoId,
      type: state.vidType, //1优学派 2菁优网
      subject,
      pointId,
      viewType: true, //true:观看次数+1；false:增加观看次数
      chapterId: ''
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      return
    }
    setUserVideoViewNumberApi(param)
  }
  // 记录每日查看视频数量
  const analyseVideoNum = () => {
    const { subject } = state
    analyseVideoNumApi({ subject })
  }
  //记录视频播放状态
  const videoScoreSave = async() => {
    await analyseVideoNum()
    //  setUserVideoViewNumber()
    setTimeout(() => {
      setUserVideoViewNumber()
    }, 2000);

  }

  // 知识点例题-切换视频
  const wekePlay2 = (data:any) => {

    const info = data
    const { cover, videoId, videoName3, type } = info
    // if (videoId == data.videoId) {
    //   return
    // }
    //改变学习状态
    if (info.status != 2) {
      info.status = 1
    }

    //跳转第0秒
    state.dp?.seek(0)
    setData({
      slider: 0,
      vidInfo: info
    })
    getVideoUrl(info)
  }
  // 设置学习状态
  const setStudyState = (status : any) => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, dataType, title, vidType, vidInfo,source } = state
    const param = {
      chapterId,
      pointId,
      videoId:vidInfo.videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidInfo?.source, //1同步章节 2知识点
      knowledgeName: title,
      status, //0未学习 1正在学 2已学完
    }

    if (vidInfo?.status != 2) {
      videoModifyApi(param)
        .then((modifyRes: any) => {
          // console.log("modifyRes--", modifyRes)
          const curVidInfo = state.vidInfo
          if (modifyRes.code == 200) {
            // curVidInfo.userCollect = modifyRes.data?.userCollect
            // curVidInfo.userLike = modifyRes.data?.userLike
            curVidInfo.status=modifyRes?.data?.status
          }
          // console.log("curVidInfo--", curVidInfo)
          //改变学习状态
          // vidInfo.status = status
          // console.log("state.data--",data)
          setData({
            data,
            vidInfo:curVidInfo
          })
        })
    }
  }


  // 点赞
  const setThumbs = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, title, vidType,source } = state
    const param : any = {
      chapterId,
      pointId,
      videoId:vidInfo?.videoId,
      type: vidInfo?.type, //1优学派 2菁优网
      subject,
      source: vidInfo?.source, //1同步章节 2知识点
      knowledgeName: vidInfo.title,
      viewType: true,
      time: 0,
      status:vidInfo.status
    }
    const isThumbs = vidInfo.userLike ? 0 : 1
    ElMessage.closeAll()
    if (isThumbs) {
      param.like = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('点赞成功')
          state.vidInfo.likeNum++
          state.vidInfo.userLike = true
        })
    } else {
      param.like = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消点赞')
          state.vidInfo.likeNum--
          state.vidInfo.userLike = false
        })
    }
  }
  // 收藏
  const setCollect = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, vidType ,source} = state
    const param : any = {
      chapterId,
      pointId,
      videoId:vidInfo?.videoId,
      type: vidInfo?.type, //1优学派 2菁优网
      subject,
      source: vidInfo?.source ,//1同步章节 2知识点
      viewType: true,
      time: 0,
      status:vidInfo.status
    }
    param.knowledgeName = vidInfo.title
    const isCollect = vidInfo.userCollect ? 0 : 1
    if (isCollect) {
      param.collect = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('收藏成功')
          state.vidInfo.userCollect = true
        })
    } else {
      param.collect = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          state.vidInfo.userCollect = false
        })
    }
  }

  //组件-播放
  const videoPlay = () => {
    if (state.videoUrl) {
      // state.dp.play()
      //自动播放-延迟且用户点击
      setTimeout(()=>{
        let video:any=document.getElementById('video')
        video.play()
      },100)
      setData({
        videoBtn: false
      })
    } else {
      ElMessage.error('请先选择一个视频')
    }
  }

  //组件-暂停
  const videoPause = () => {
    state.dp.pause()
    setData({
      videoBtn: true
    })
  }

  function initPlayers() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let window1 : any = window
    let DPlayer : any = window1?.DPlayer
    state.dp = new DPlayer({
      container: document.getElementById('dplayer1'),
      autoplay: true, //没效果
      theme: '#1DDFAC', //进度条、音量颜色
      preload: 'auto',
      volume: 1,
      contextmenu: [],
      playbackRates: [0.5,1, 1.25, 1.5, 2],
      video: {
        title: videoName3 || videoName,
        url: state.videoUrl,
        pic: cover,
        type: type == 1 ? 'hls' : 'normal'
      }
    });

    state.dp.on('loadstart', function () {
      showPlayIcon()
      // 修改设置为倍速弹窗
      let setbtn : any = document.querySelector('.dplayer-setting-icon')
      setbtn.addEventListener('mouseenter', function () {
        if (document.querySelectorAll('.dplayer-hide-controller').length) {
          // 底部菜单隐藏时不显示
          return
        }
        //显示倍速弹窗
        let setting : any = document.querySelector('.dplayer-setting-box')
        setting.className =
          'dplayer-setting-box dplayer-setting-box-narrow dplayer-setting-box-speed dplayer-setting-box-open'
      })
      //倍速弹窗hover隐藏
      let setting : any = document.querySelector('.dplayer-setting-box')
      setting.addEventListener('mouseleave', function () {
        setting.className = 'dplayer-setting-box'
      })
      setting.addEventListener('click', function () {
        setting.className = 'dplayer-setting-box'
      })

      //隐藏视频标题
      let vidClass : any = document.querySelector('.dplayer-video-current')
      vidClass.addEventListener('mouseleave', function () {
        if (document.querySelectorAll('.dplayer-paused').length) {
          // 暂停时不隐藏
          return
        }
        let title : any = document.getElementById('fulltit')
        title.style.display = 'none'
      })
    })

    //设置倍速文字
    state.dp.on('ratechange', function () {
      speedChange('')
    });

    state.dp.on('ended', function () {
      // console.log('结束');
      videoPause()
      if (query?.sourceType !== 'collect') { 
        setStudyState(2)
        setTimeout(() => {
            getVideoList('reset')
        },500)
        setJfShow2()
      }

    });
    state.dp.on('pause', function () {
      showPlayIcon()
    });
    state.dp.on('play', function () {
      // console.log('开始播放');
      hidePlayIcon()
    });

    state.dp.on('volumechange', function () {
      // console.log('音量切换');
    })
  }

  fullScreenListeners()
  //监听全屏
  function fullScreenListeners() {
    document.addEventListener('fullscreenchange', function () {
      let fullIcon : any = document.querySelector('#fullIcon')
      if (document.fullscreenElement) {
        fullIcon.setAttribute('data-title', '退出全屏')
      } else {
        fullIcon.setAttribute('data-title', '全屏')
      }
    })
  }

  //显示播放图标
  function showPlayIcon() {
    let icon : any = document.querySelector('.dplayer-bezel-icon')
    if(icon){
      icon.className = 'dplayer-bezel-icon play'
    }
  }

  //隐藏播放图标
  function hidePlayIcon() {
    setTimeout(() => {
      let icon : any = document.querySelector('.dplayer-bezel-icon')
      if(icon){
        icon.className = 'dplayer-bezel-icon'
      }
    }, 0)
  }

  //切换视频
  function switchVideo() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let obj = {
      title: videoName3 || videoName,
      url: state.videoUrl,
      pic: cover,
      type: type == 1 ? 'hls' : 'normal'
    }
    let src = state.dp.video.src
    if (src == obj.url) {
      return
    }
    state.dp.switchVideo(obj);
    speedChange(1)
    //自动播放-必须延迟且是用户点击
    setTimeout(()=>{
      let video:any=document.getElementById('video')
      video.play()
    },100)
  }
  // 倍速切换
  function speedChange(num : any) {
    let video : any = document.getElementById('video')
    let speed = num || video.playbackRate
    let sptxt : any = document.querySelector('.dplayer-icon.dplayer-setting-icon')
    if (speed == 1) {
      sptxt.innerHTML = '倍速'
    } else if (speed == 2) {
      sptxt.innerHTML = '2.0X'
    } else {
      sptxt.innerHTML = speed + 'X'
    }
    //倍速弹窗选中变色
    let list : any = document.querySelectorAll('.dplayer-setting-speed-item')
    for (let i = 0; i < list.length; i++) {
      let num = Number(list[i].attributes['data-speed'].value)
      if (num == speed) {
        list[i].className = 'dplayer-setting-speed-item green'
      } else {
        list[i].className = 'dplayer-setting-speed-item'
      }
    }
  }
  //视频控件e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .nowrap2 {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
    position: relative;
  }

  .wrap {
    float: left;
    width: 100%;
    display: flex;
  }

  .wrap>div {
    margin-right: 10px;
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
  }

  /* 视频 */
  .menu_lt {
    float: left;
    width: calc(100% - 21.125rem - .625rem);
    padding: .625rem;
    position: sticky;
    top: 0;
    height: calc(100vh - 70px);
    overflow-y: auto;
  }

  .menu_lt.full-width {
    width: calc(100% - 1.25rem);
  }

  .wk_box {
    width: 100%;
  }

  .wk_box div {
    float: left;
  }

  .wk_tit {
    width: 100%;
  }

  .wk_p {
    line-height: 1.375rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 .625rem 0 0;
  }

  .wk_status {
    width: 2.875rem;
    line-height: 1.375rem;
    border-radius: .25rem;
    text-align: center;
    font-size: .75rem;
  }

  .wk_status.status0 {
    color: #fff;
    background: #999;
  }

  .wk_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .wk_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .wk_video {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
  }

  .wk_nodata {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .wk_nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 点赞收藏 */
  .wk_opt {
    width: 100%;
    height: 4rem;
    box-sizing: border-box;
    padding: 1.25rem .625rem;
    border-bottom: .0625rem solid #eaeaea;
  }

  .wk_collect {
    margin: 0 3.125rem 0 0;
    cursor: pointer;
  }

  .wk_collect img {
    width: 1.5rem;
    height: 1.5rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  .wk_thumbs {
    line-height: 1.5rem;
    color: #666666;
    font-size: .875rem;
    float: right;
    cursor: pointer;
  }

  .wk_thumbs img {
    float: left;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  /* 知识点 */
  .iframe {
    border: 0;
    width: 100%;
    height: 100vh;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }

  /* 正在播放 */
  .menu_rt {
    float: right;
    width: 21.125rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    height: calc(100vh - 70px);
    overflow-y: auto;
  }

  .menu_rt div {
    float: left;
  }

  .now_box {
    width: 100%;
    border-bottom: .0625rem solid #eaeaea;
  }

  .now_air {
    width: 100%;
    line-height: 1rem;
    color: #5a85ec;
    font-size: 1rem;
    margin: .8125rem 0 1.25rem;
  }

  .now_air img {
    float: left;
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 .625rem;
  }

  .now_img {
    width: 19.875rem;
    height: 11.1875rem;
    border-radius: .25rem;
    border: .0625rem solid #5a85ec;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 .625rem;
  }

  .now_img img {
    float: left;
    width: 19.875rem;
    height: 11.1875rem;
  }

  .now_name {
    width: 100%;
    line-height: 1.1875rem;
    color: #5a85ec;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .375rem .625rem 1.25rem;
  }

  div.now_status {
    float: right;
    width: 3.75rem;
    line-height: 1.5rem;
    border-radius: 0 0 0 .25rem;
    text-align: center;
    font-size: .75rem;
    color: #fff;
    position: relative;
    z-index: 2;
    margin: -11.1875rem 0 0;
  }

  .now_status.status0 {
    background: #999;
  }

  .now_status.status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .now_status.status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  /* 列表 */
  .vid_h2 {
    width: calc(100% - 1.25rem);
    line-height: 1.1875rem;
    border-radius: .25rem;
    background: #f5f5f5;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .5625rem .625rem;
    margin: .625rem 0 0 .625rem;
  }

  .vid_ul {
    width: 100%;
    box-sizing: border-box;
    padding: .625rem;
  }

  .vid_li {
    width: 100%;
    height: 5.0625rem;
    overflow: hidden;
    margin: 0 0 .625rem;
    cursor: pointer;
    display: flex;
  }

  .vid_li:last-child {
    margin: 0;
  }
  /* 语文同步学习特殊样式 */
  .chinese-sync-card {
    background: #e9e9e9;
    width: 100%;
    height: 5.0625rem;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 4px; /* 与wk_li的圆角保持一致 */
  }

  .chinese-sync-title {
    color: #2a2b2a;
    font-size: 40px;
    font-weight: 600;
    text-align: center;
    width: 50px;
    height: 50px;
    line-height: 50px;
    max-width: 85%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    background-size: cover;
  }
  .english-sync-title{
    color: #2a2b2a;
    font-size: 14px;
    text-align: center;
    width: 209px;
    height: 50px;
    line-height: 50px;
    max-width: 85%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    background-size: cover;
  }

  /* 语文同步学习容器 */
  .chinese-sync-container {
    position: relative;
  }

  /* 语文同步学习样式下的查看详情按钮调整 */
  .chinese-sync-view {
    position: absolute;
    top: 5px;
    left: 5px;
    z-index: 20;
  }

  /* 语文同步学习样式下的播放按钮调整 */
  .chinese-sync-play {
    position: absolute;
    top: auto;
    bottom: 10px;
    right: 10px;
    z-index: 20;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0; /* 重置默认margin */
  }

  .vid_img,
  .vid_img img {
    // float: left;
    width: 9rem;
    height: 5.0625rem;
    border-radius: .25rem;
  }

  .vid_rt {
    width: 10.375rem;
    margin: 0 0 0 .375rem;
  }

  .vid_name {
    width: 10.5rem;
    max-height: 4rem;
    line-height: 1.5;
    color: #2a2b2a;
    font-size: .75rem;
  }

  .vid_li .vid_name {
    color: rgba(42, 43, 42, 1);
    font-size: 14px;
  }

  .vid_state {
    width: 100%;
    margin: 6px 0 0;
  }

  .vid_status {
    width: 2.875rem;
    line-height: 1.3125rem;
    border-radius: .25rem;
    text-align: center;
    color: #ffffff;
    font-size: .75rem;
  }

  .vid_status.status0 {
    color: #fff;
    background: #999;
  }

  .vid_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .vid_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .vid_h1 {
    line-height: 1.3125rem;
    color: #999999;
    font-size: .75rem;
    margin: 0 .3125rem 0 .625rem;
  }

  div.vid_thumbs {
    float: right;
    line-height: 1.125rem;
    color: #666666;
    font-size: .75rem;
    float: right;
    margin-right: 10px;
  }

  .vid_thumbs img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .375rem 0 0;
  }

  .vid_thumbs img:nth-child(1),
  .vid_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .vid_thumbs img:nth-child(2),
  .vid_thumbs.active img:nth-child(1) {
    display: none;
  }

  .vid_tit:hover,
  .vid_thumbs img:hover {
    cursor: pointer;
  }
</style>
